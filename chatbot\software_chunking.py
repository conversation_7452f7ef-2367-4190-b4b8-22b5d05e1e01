import mysql.connector
import hashlib
import fitz  # PyMuPDF
import spacy
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# Load spaCy once
nlp = spacy.load("en_core_web_sm")

# MySQL connection setup
conn = mysql.connector.connect(
    host="localhost",
    user="root",
    password="phoobesh333",
    database="rough"
)
cursor = conn.cursor()

# ---------- MySQL Helpers ----------

def fetch_software_pdf_files_from_db():
    """Fetch software PDF files stored as BLOBs from MySQL."""
    cursor.execute("SELECT id, file_name, file_data, camera_type FROM pdf_files WHERE camera_type = 'software'")
    return cursor.fetchall()

def fetch_existing_hashes():
    """Fetch existing file hashes to avoid reprocessing."""
    cursor.execute("SELECT DISTINCT file_hash FROM pdf_chunks")
    return {row[0] for row in cursor.fetchall()}

def insert_chunks_to_db(chunks):
    """Insert chunks into MySQL database."""
    if not chunks:
        return
    
    insert_query = """
    INSERT INTO pdf_chunks (
        source_file, chunk_number, content, file_hash, last_modified,
        created_at, camera_type, page_number, section_title, vector_embedded, chunked
    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """

    values = []
    for chunk in chunks:
        values.append((
            chunk["source_file"],
            chunk["chunk_number"],
            chunk["content"],
            chunk["file_hash"],
            chunk["last_modified"],
            chunk["created_at"],
            chunk["camera_type"],
            chunk.get("page_number", 1),
            chunk.get("section_title", "Unknown"),
            0,  # vector_embedded = False initially
            1   # chunked = True
        ))
    
    cursor.executemany(insert_query, values)
    conn.commit()
    print(f"✅ Inserted {len(chunks)} software chunks into database")

# ---------- Utility Functions ----------

def calculate_sha256_from_bytes(data):
    """Calculate SHA-256 from byte content."""
    return hashlib.sha256(data).hexdigest()

def extract_blocks_from_software_pdf(pdf_bytes):
    """Extract text blocks with page numbers, optimized for software documentation."""
    pdf_document = fitz.open("pdf", pdf_bytes)
    results = []
    
    for page_number, page in enumerate(pdf_document, start=1):
        blocks = page.get_text("blocks")
        for block in blocks:
            text = block[4].strip()
            if not text:
                continue
            
            # Software-specific section detection
            section_title = None
            text_upper = text.upper()
            
            # Detect common software documentation sections
            if any(keyword in text_upper for keyword in [
                "API REFERENCE", "FUNCTION", "METHOD", "CLASS", "NAMESPACE",
                "PARAMETERS", "RETURNS", "EXAMPLE", "CODE SAMPLE", "TUTORIAL",
                "INSTALLATION", "SETUP", "CONFIGURATION", "TROUBLESHOOTING"
            ]):
                if len(text.split()) <= 10:  # Short titles
                    section_title = text
            
            results.append({
                "page_number": page_number,
                "text": text,
                "section_title": section_title
            })
    
    pdf_document.close()
    return results

def split_with_spacy_software(text):
    """Split text into sentences using spaCy, optimized for software docs."""
    doc = nlp(text)
    sentences = []
    
    for sent in doc.sents:
        sent_text = sent.text.strip()
        if sent_text:
            # Keep code blocks together
            if any(keyword in sent_text for keyword in ["```", "```", "class ", "function ", "def ", "public ", "private "]):
                sentences.append(sent_text)
            else:
                sentences.append(sent_text)
    
    return sentences

def regroup_sentences_software(sentences, max_words=300):
    """Regroup sentences into chunks optimized for software documentation."""
    chunks, current_chunk, word_count = [], [], 0
    
    for sent in sentences:
        words = sent.split()
        
        # Keep code blocks together
        if any(keyword in sent for keyword in ["```", "class ", "function ", "def ", "public ", "private "]):
            # If current chunk exists, finalize it
            if current_chunk:
                chunks.append(" ".join(current_chunk))
                current_chunk = []
                word_count = 0
            
            # Start new chunk with code block
            current_chunk = [sent]
            word_count = len(words)
        elif word_count + len(words) <= max_words:
            current_chunk.append(sent)
            word_count += len(words)
        else:
            if current_chunk:
                chunks.append(" ".join(current_chunk))
            current_chunk = [sent]
            word_count = len(words)
    
    if current_chunk:
        chunks.append(" ".join(current_chunk))
    
    return chunks

# ---------- Main Software PDF Chunking Logic ----------

def process_software_pdf(record):
    """Process a single software PDF record."""
    id_, file_name, file_data, camera_type = record
    file_hash = calculate_sha256_from_bytes(file_data)
    last_modified_str = datetime.now().isoformat()
    created_at_str = datetime.utcnow().isoformat()
    
    print(f"Processing software document: {file_name}...")
    
    try:
        blocks = extract_blocks_from_software_pdf(file_data)
        if not blocks:
            print(f"⚠️ No text blocks found in {file_name}")
            return []
        
        processed_chunks = []
        for block in blocks:
            sentences = split_with_spacy_software(block["text"])
            sentence_chunks = regroup_sentences_software(sentences, max_words=300)
            
            for chunk_text in sentence_chunks:
                processed_chunks.append({
                    "content": chunk_text,
                    "page_number": block["page_number"],
                    "section_title": block["section_title"] or "Unknown"
                })
        
        return [{
            "source_file": file_name,
            "chunk_number": f"{idx+1}",
            "content": chunk["content"],
            "file_hash": file_hash,
            "last_modified": last_modified_str,
            "created_at": created_at_str,
            "camera_type": camera_type,
            "page_number": chunk["page_number"],
            "section_title": chunk["section_title"]
        } for idx, chunk in enumerate(processed_chunks)]
        
    except Exception as e:
        print(f"❌ Error processing {file_name}: {e}")
        return []

def process_all_software_pdfs_from_db():
    """Process all software PDF files from the database."""
    pdf_records = fetch_software_pdf_files_from_db()
    if not pdf_records:
        print("No software PDF records found in DB.")
        return
    
    existing_hashes = fetch_existing_hashes()
    new_chunks = []
    
    def process_record(record):
        id_, file_name, file_data, camera_type = record
        file_hash = calculate_sha256_from_bytes(file_data)
        
        if file_hash in existing_hashes:
            print(f"Skipping {file_name} (no content changes).")
            return []
        
        return process_software_pdf(record)
    
    # Process files in parallel
    with ThreadPoolExecutor(max_workers=4) as executor:
        results = executor.map(process_record, pdf_records)
        for result in results:
            new_chunks.extend(result)
    
    if new_chunks:
        insert_chunks_to_db(new_chunks)
        print(f"🎉 Successfully processed {len(new_chunks)} software chunks from {len(pdf_records)} files.")
    else:
        print("No new software chunks to process.")

# ---------- Main Entry Point ----------

if __name__ == "__main__":
    print("🚀 Starting software PDF chunking process...")
    process_all_software_pdfs_from_db()
    print("✅ Software chunking process completed.")
    
    # Close database connection
    cursor.close()
    conn.close()
