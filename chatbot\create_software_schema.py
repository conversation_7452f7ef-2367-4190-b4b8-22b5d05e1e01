import weaviate

# Configuration
WEAVIATE_URL = "http://localhost:8080"
SOFTWARE_CHUNK_CLASS = "SoftwareChunkV1"

def create_software_schema():
    """Create SoftwareChunkV1 class in Weaviate."""
    try:
        client = weaviate.Client(url=WEAVIATE_URL)
        
        # Delete existing class if it exists (to recreate properly)
        try:
            client.schema.delete_class(SOFTWARE_CHUNK_CLASS)
            print(f"🗑️ Deleted existing {SOFTWARE_CHUNK_CLASS} class")
        except:
            print(f"ℹ️ {SOFTWARE_CHUNK_CLASS} class doesn't exist yet")
        
        # Create the software class
        software_class = {
            "class": SOFTWARE_CHUNK_CLASS,
            "description": "Software documentation chunks with software-specific metadata",
            "properties": [
                {"name": "source_file", "dataType": ["text"], "description": "Filename of software document"},
                {"name": "chunk_number", "dataType": ["int"], "description": "Index of the chunk"},
                {"name": "content", "dataType": ["text"], "description": "Actual chunk text"},
                {"name": "software_name", "dataType": ["text"], "description": "Software name"},
                {"name": "software_version", "dataType": ["text"], "description": "Software version"},
                {"name": "api_type", "dataType": ["text"], "description": "API type"},
                {"name": "module_name", "dataType": ["text"], "description": "Module or feature name"},
                {"name": "document_type", "dataType": ["text"], "description": "Type of document"},
                {"name": "created_at", "dataType": ["text"], "description": "Timestamp of ingestion"}
            ],
            "vectorizer": "none"
        }
        
        client.schema.create_class(software_class)
        print(f"✅ Successfully created {SOFTWARE_CHUNK_CLASS} class")
        
        # Verify creation
        schema = client.schema.get()
        existing_classes = [cls['class'] for cls in schema.get('classes', [])]
        if SOFTWARE_CHUNK_CLASS in existing_classes:
            print(f"✅ Verified: {SOFTWARE_CHUNK_CLASS} class exists")
        else:
            print(f"❌ Error: {SOFTWARE_CHUNK_CLASS} class not found after creation")
            
    except Exception as e:
        print(f"❌ Error creating software schema: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_software_schema()
